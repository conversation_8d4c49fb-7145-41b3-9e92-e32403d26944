using BambooHR.Models;
using BambooHR.Services;
using Moq;
using Xunit;

namespace BambooHR.Tests;

public class OrderServiceTests
{
    [Fact]
    public async Task CreateOrderAsync_ShouldCreateOrderWithPendingStatus()
    {
        // Arrange
        var orderService = new OrderService();
        var order = new Order
        {
            CustomerId = 1,
            OrderDate = DateTime.UtcNow,
            TotalAmount = 100.00m,
            Items = new List<OrderItem>
            {
                new OrderItem { ProductId = 1, Quantity = 2, UnitPrice = 50.00m, TotalPrice = 100.00m }
            }
        };

        // Act
        var result = await orderService.CreateOrderAsync(order);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(OrderStatus.Pending, result.Status);
        Assert.True(result.Id > 0);
        Assert.Equal(order.CustomerId, result.CustomerId);
        Assert.Equal(order.TotalAmount, result.TotalAmount);
    }

    [Fact]
    public async Task GetOrderByIdAsync_ShouldReturnOrder_WhenOrderExists()
    {
        // Arrange
        var orderService = new OrderService();
        var order = new Order
        {
            CustomerId = 1,
            OrderDate = DateTime.UtcNow,
            TotalAmount = 100.00m
        };
        var createdOrder = await orderService.CreateOrderAsync(order);

        // Act
        var result = await orderService.GetOrderByIdAsync(createdOrder.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(createdOrder.Id, result.Id);
        Assert.Equal(createdOrder.CustomerId, result.CustomerId);
    }

    [Fact]
    public async Task GetOrderByIdAsync_ShouldReturnNull_WhenOrderDoesNotExist()
    {
        // Arrange
        var orderService = new OrderService();

        // Act
        var result = await orderService.GetOrderByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ConfirmOrderAsync_ShouldChangeStatusToConfirmed()
    {
        // Arrange
        var orderService = new OrderService();
        var order = new Order
        {
            CustomerId = 1,
            OrderDate = DateTime.UtcNow,
            TotalAmount = 100.00m
        };
        var createdOrder = await orderService.CreateOrderAsync(order);

        // Act
        var result = await orderService.ConfirmOrderAsync(createdOrder.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(OrderStatus.Confirmed, result.Status);
        Assert.Equal(createdOrder.Id, result.Id);
    }

    [Fact]
    public async Task ConfirmOrderAsync_ShouldThrowException_WhenOrderDoesNotExist()
    {
        // Arrange
        var orderService = new OrderService();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => orderService.ConfirmOrderAsync(999));
    }

    [Fact]
    public async Task GetOrdersByCustomerIdAsync_ShouldReturnCustomerOrders()
    {
        // Arrange
        var orderService = new OrderService();
        var customerId = 1;
        var order1 = new Order { CustomerId = customerId, OrderDate = DateTime.UtcNow, TotalAmount = 100.00m };
        var order2 = new Order { CustomerId = customerId, OrderDate = DateTime.UtcNow, TotalAmount = 200.00m };
        var order3 = new Order { CustomerId = 2, OrderDate = DateTime.UtcNow, TotalAmount = 150.00m };

        await orderService.CreateOrderAsync(order1);
        await orderService.CreateOrderAsync(order2);
        await orderService.CreateOrderAsync(order3);

        // Act
        var result = await orderService.GetOrdersByCustomerIdAsync(customerId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.All(result, order => Assert.Equal(customerId, order.CustomerId));
    }
}
