using BambooHR.Models;

namespace BambooHR.Services;

public class OrderService : IOrderService
{
    private readonly List<Order> _orders = new();
    private int _nextId = 1;

    public Task<Order> CreateOrderAsync(Order order)
    {
        order.Id = _nextId++;
        order.Status = OrderStatus.Pending;
        order.CreatedAt = DateTime.UtcNow;
        order.UpdatedAt = DateTime.UtcNow;

        _orders.Add(order);

        return Task.FromResult(order);
    }

    public Task<Order?> GetOrderByIdAsync(int orderId)
    {
        var order = _orders.FirstOrDefault(o => o.Id == orderId);
        return Task.FromResult(order);
    }

    public Task<Order> ConfirmOrderAsync(int orderId)
    {
        var order = _orders.FirstOrDefault(o => o.Id == orderId);
        if (order == null)
        {
            throw new InvalidOperationException($"Order with ID {orderId} not found.");
        }

        order.Status = OrderStatus.Confirmed;
        order.UpdatedAt = DateTime.UtcNow;

        return Task.FromResult(order);
    }

    public Task<IEnumerable<Order>> GetOrdersByCustomerIdAsync(int customerId)
    {
        var orders = _orders.Where(o => o.CustomerId == customerId);
        return Task.FromResult(orders);
    }
}
