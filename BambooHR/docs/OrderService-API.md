# OrderService API Documentation

## Overview

The `OrderService` is a core service in the BambooHR order processing workflow system. It provides comprehensive order management functionality including order creation, retrieval, confirmation, and customer-specific order queries.

## Interface

```csharp
public interface IOrderService
{
    Task<Order> CreateOrderAsync(Order order);
    Task<Order?> GetOrderByIdAsync(int orderId);
    Task<Order> ConfirmOrderAsync(int orderId);
    Task<IEnumerable<Order>> GetOrdersByCustomerIdAsync(int customerId);
}
```

## Data Models

### Order

```csharp
public class Order
{
    public int Id { get; set; }
    public int CustomerId { get; set; }
    public DateTime OrderDate { get; set; }
    public OrderStatus Status { get; set; }
    public decimal TotalAmount { get; set; }
    public List<OrderItem> Items { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

### OrderStatus

```csharp
public enum OrderStatus
{
    Pending,
    Confirmed,
    Paid,
    Fulfilled,
    Cancelled
}
```

### OrderItem

```csharp
public class OrderItem
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public int ProductId { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
}
```

## API Methods

### CreateOrderAsync

Creates a new order in the system.

**Signature:**
```csharp
Task<Order> CreateOrderAsync(Order order)
```

**Parameters:**
- `order` (Order): The order object to create

**Returns:**
- `Task<Order>`: The created order with assigned ID and system-generated fields

**Behavior:**
- Assigns a unique ID to the order
- Sets the order status to `Pending`
- Sets `CreatedAt` and `UpdatedAt` timestamps to current UTC time
- Stores the order in the system

**Example Usage:**
```csharp
var order = new Order
{
    CustomerId = 123,
    OrderDate = DateTime.UtcNow,
    TotalAmount = 299.99m,
    Items = new List<OrderItem>
    {
        new OrderItem 
        { 
            ProductId = 1, 
            Quantity = 2, 
            UnitPrice = 149.99m, 
            TotalPrice = 299.98m 
        }
    }
};

var createdOrder = await orderService.CreateOrderAsync(order);
// createdOrder.Id will be assigned
// createdOrder.Status will be OrderStatus.Pending
```

### GetOrderByIdAsync

Retrieves an order by its unique identifier.

**Signature:**
```csharp
Task<Order?> GetOrderByIdAsync(int orderId)
```

**Parameters:**
- `orderId` (int): The unique identifier of the order

**Returns:**
- `Task<Order?>`: The order if found, otherwise `null`

**Example Usage:**
```csharp
var order = await orderService.GetOrderByIdAsync(123);
if (order != null)
{
    Console.WriteLine($"Order {order.Id} has status {order.Status}");
}
else
{
    Console.WriteLine("Order not found");
}
```

### ConfirmOrderAsync

Confirms an existing order, changing its status from `Pending` to `Confirmed`.

**Signature:**
```csharp
Task<Order> ConfirmOrderAsync(int orderId)
```

**Parameters:**
- `orderId` (int): The unique identifier of the order to confirm

**Returns:**
- `Task<Order>`: The confirmed order with updated status

**Exceptions:**
- `InvalidOperationException`: Thrown when the order with the specified ID is not found

**Behavior:**
- Changes the order status to `Confirmed`
- Updates the `UpdatedAt` timestamp to current UTC time

**Example Usage:**
```csharp
try
{
    var confirmedOrder = await orderService.ConfirmOrderAsync(123);
    Console.WriteLine($"Order {confirmedOrder.Id} confirmed successfully");
}
catch (InvalidOperationException ex)
{
    Console.WriteLine($"Error: {ex.Message}");
}
```

### GetOrdersByCustomerIdAsync

Retrieves all orders for a specific customer.

**Signature:**
```csharp
Task<IEnumerable<Order>> GetOrdersByCustomerIdAsync(int customerId)
```

**Parameters:**
- `customerId` (int): The unique identifier of the customer

**Returns:**
- `Task<IEnumerable<Order>>`: A collection of orders belonging to the customer

**Example Usage:**
```csharp
var customerOrders = await orderService.GetOrdersByCustomerIdAsync(123);
foreach (var order in customerOrders)
{
    Console.WriteLine($"Order {order.Id}: {order.Status} - ${order.TotalAmount}");
}
```

## Order Workflow Integration

The `OrderService` is the first step in the three-phase order processing workflow:

1. **Order Confirmation** (OrderService) - Creates and confirms orders
2. **Payment Processing** (PaymentService) - Processes payments for confirmed orders
3. **Inventory Update** (InventoryService) - Updates stock levels after successful payment

## Error Handling

The service implements the following error handling patterns:

- **Not Found**: Returns `null` for `GetOrderByIdAsync` when order doesn't exist
- **Invalid Operations**: Throws `InvalidOperationException` for operations on non-existent orders
- **Validation**: Input validation should be handled at the API controller level

## Thread Safety

The current implementation uses instance-level collections and is **not thread-safe**. In a production environment, this should be replaced with:

- Database persistence layer
- Proper concurrency handling
- Transaction management

## Testing

The service includes comprehensive unit tests covering:

- Order creation with proper status assignment
- Order retrieval by ID (both found and not found scenarios)
- Order confirmation with status updates
- Exception handling for invalid operations
- Customer-specific order queries

## Future Enhancements

Planned improvements include:

- Database persistence integration
- Order validation rules
- Status transition validation
- Audit logging
- Bulk operations support
- Pagination for customer order queries

## Dependencies

- .NET 9.0
- No external dependencies (current in-memory implementation)

## Version History

- **v1.0.0**: Initial implementation with in-memory storage
  - Basic CRUD operations
  - Order confirmation workflow
  - Customer order queries
